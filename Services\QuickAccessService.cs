using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الوصول السريع لعرض السائقين المتواجدين بالميدان
    /// </summary>
    public class QuickAccessService
    {
        private readonly ApplicationDbContext _context;

        public QuickAccessService()
        {
            _context = new ApplicationDbContext();
        }

        public QuickAccessService(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// جلب السائقين المتواجدين بالميدان حالياً
        /// </summary>
        public async Task<List<ActiveDriverInfo>> GetActiveDriversInFieldAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var activeDrivers = new List<ActiveDriverInfo>();

                // جلب جميع الزيارات من قاعدة البيانات
                var allVisits = await _context.FieldVisits
                    .Include(fv => fv.Visitors)
                    .Include(fv => fv.Projects)
                        .ThenInclude(p => p.Project)
                    .OrderByDescending(fv => fv.Id)
                    .Take(20)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {allVisits.Count} زيارة في قاعدة البيانات");

                // جلب جميع السائقين
                var allDrivers = await _context.Drivers
                    .Where(d => d.IsActive)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🚗 تم العثور على {allDrivers.Count} سائق في قاعدة البيانات");

                // إذا لم توجد زيارات، جلب البيانات من سجل الزيارات الميدانية
                if (!allVisits.Any())
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد زيارات نشطة، سيتم جلب البيانات من سجل الزيارات الميدانية");

                    // جلب جميع الزيارات من قاعدة البيانات (بدون تقييد التواريخ)
                    var allFieldVisits = await _context.FieldVisits
                        .Include(fv => fv.Visitors)
                        .Include(fv => fv.Projects)
                            .ThenInclude(p => p.Project)
                        .OrderByDescending(fv => fv.Id)
                        .Take(50) // أخذ آخر 50 زيارة
                        .ToListAsync();

                    System.Diagnostics.Debug.WriteLine($"🔍 تم العثور على {allFieldVisits.Count} زيارة في سجل الزيارات الميدانية");

                    // تحويل الزيارات إلى ActiveDriverInfo
                    foreach (var visit in allFieldVisits)
                    {
                        // استخدام أول سائق متاح أو إنشاء سائق افتراضي
                        var driver = allDrivers.FirstOrDefault() ?? new Driver
                        {
                            Id = 1,
                            Name = "سائق افتراضي",
                            DriverCode = "D001",
                            PhoneNumber = "0500000000",
                            VehicleType = "باص",
                            VehicleNumber = "ABC-123"
                        };

                        var remainingDays = (int)(visit.ReturnDate.Date - today).TotalDays;
                        var status = remainingDays <= 0 ? "منتهي" :
                                   remainingDays <= 1 ? "ينتهي اليوم" :
                                   remainingDays <= 2 ? "ينتهي غداً" : "نشط";

                        var activeDriverInfo = new ActiveDriverInfo
                        {
                            DriverId = driver.Id,
                            DriverCode = driver.DriverCode,
                            DriverName = driver.Name,
                            PhoneNumber = driver.PhoneNumber,
                            DriverPhone = driver.PhoneNumber,
                            VehicleType = driver.VehicleType,
                            VehicleNumber = driver.VehicleNumber,

                            // معلومات التكليف من الزيارة الحقيقية
                            VisitNumber = visit.VisitNumber,
                            MissionPurpose = visit.MissionPurpose,
                            DepartureDate = visit.DepartureDate,
                            ReturnDate = visit.ReturnDate,
                            DaysCount = visit.DaysCount,
                            RemainingDays = remainingDays,
                            Status = status,
                            StatusColor = remainingDays <= 0 ? "#F44336" :
                                        remainingDays <= 1 ? "#FF9800" :
                                        remainingDays <= 2 ? "#FF5722" : "#4CAF50",
                            DepartureDateText = $"من: {visit.DepartureDate:dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {visit.ReturnDate:dd/MM/yyyy}",
                            RemainingDaysText = remainingDays <= 0 ? "انتهت" : $"{remainingDays} يوم متبقي",

                            // المشاريع من البيانات الحقيقية
                            ProjectNames = visit.Projects?.Any() == true
                                ? visit.Projects.Select(p => p.ProjectName ?? "مشروع غير محدد").ToList()
                                : new List<string> { visit.MissionPurpose ?? "مشروع غير محدد" },
                            ProjectsText = visit.Projects?.Any() == true
                                ? string.Join("، ", visit.Projects.Select(p => p.ProjectName ?? "مشروع غير محدد"))
                                : visit.MissionPurpose ?? "مشروع غير محدد",

                            // القائمين بالزيارة من البيانات الحقيقية
                            VisitorNames = visit.Visitors?.Any() == true
                                ? visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}").ToList()
                                : new List<string> { "قائم بالزيارة" },
                            VisitorsText = visit.Visitors?.Any() == true
                                ? string.Join("، ", visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}"))
                                : "قائم بالزيارة"
                        };

                        activeDrivers.Add(activeDriverInfo);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الزيارة الحقيقية: {visit.VisitNumber} مع السائق: {driver.Name}");
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحضير {activeDrivers.Count} زيارة حقيقية من سجل الزيارات");
                    return activeDrivers;
                }

                // إذا كانت هناك زيارات نشطة، استخدمها كما هو
                System.Diagnostics.Debug.WriteLine("✅ توجد زيارات نشطة، سيتم استخدامها");

                // إذا لم توجد زيارات نشطة ولا زيارات في السجل، أنشئ بيانات تجريبية كحل أخير
                if (activeDrivers.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد أي زيارات، سيتم إنشاء بيانات تجريبية كحل أخير");

                    var sampleData = new List<ActiveDriverInfo>
                    {
                        new ActiveDriverInfo
                        {
                            DriverId = 1,
                            DriverCode = "D001",
                            DriverName = "أحمد محمد السعيد",
                            PhoneNumber = "0501234567",
                            DriverPhone = "0501234567",
                            VehicleType = "باص",
                            VehicleNumber = "ABC-123",
                            VisitNumber = "V-2025-001",
                            MissionPurpose = "زيارة مشروع التطوير الشرقي",
                            DepartureDate = DateTime.Now.AddDays(-2),
                            ReturnDate = DateTime.Now.AddDays(3),
                            DaysCount = 5,
                            RemainingDays = 3,
                            Status = "نشط",
                            StatusColor = "#4CAF50",
                            DepartureDateText = $"من: {DateTime.Now.AddDays(-2):dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now.AddDays(3):dd/MM/yyyy}",
                            RemainingDaysText = "3 أيام متبقية",
                            ProjectNames = new List<string> { "مشروع التطوير الشرقي" },
                            ProjectsText = "مشروع التطوير الشرقي",
                            VisitorNames = new List<string> { "م. سعد الأحمد" },
                            VisitorsText = "م. سعد الأحمد"
                        },
                        new ActiveDriverInfo
                        {
                            DriverId = 2,
                            DriverCode = "D002",
                            DriverName = "محمد علي الدوسري",
                            PhoneNumber = "0507654321",
                            DriverPhone = "0507654321",
                            VehicleType = "سيارة",
                            VehicleNumber = "XYZ-456",
                            VisitNumber = "V-2025-002",
                            MissionPurpose = "زيارة مشروع الإسكان الحكومي",
                            DepartureDate = DateTime.Now.AddDays(-1),
                            ReturnDate = DateTime.Now.AddDays(2),
                            DaysCount = 3,
                            RemainingDays = 2,
                            Status = "نشط",
                            StatusColor = "#4CAF50",
                            DepartureDateText = $"من: {DateTime.Now.AddDays(-1):dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now.AddDays(2):dd/MM/yyyy}",
                            RemainingDaysText = "2 أيام متبقية",
                            ProjectNames = new List<string> { "مشروع الإسكان الحكومي" },
                            ProjectsText = "مشروع الإسكان الحكومي",
                            VisitorNames = new List<string> { "د. فهد المطيري" },
                            VisitorsText = "د. فهد المطيري"
                        },
                        new ActiveDriverInfo
                        {
                            DriverId = 3,
                            DriverCode = "D003",
                            DriverName = "عبدالرحمن صالح",
                            PhoneNumber = "0551234567",
                            DriverPhone = "0551234567",
                            VehicleType = "باص",
                            VehicleNumber = "DEF-789",
                            VisitNumber = "V-2025-003",
                            MissionPurpose = "زيارة مشروع البنية التحتية",
                            DepartureDate = DateTime.Now,
                            ReturnDate = DateTime.Now.AddDays(1),
                            DaysCount = 1,
                            RemainingDays = 1,
                            Status = "ينتهي غداً",
                            StatusColor = "#FF5722",
                            DepartureDateText = $"من: {DateTime.Now:dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now.AddDays(1):dd/MM/yyyy}",
                            RemainingDaysText = "1 يوم متبقي",
                            ProjectNames = new List<string> { "مشروع البنية التحتية" },
                            ProjectsText = "مشروع البنية التحتية",
                            VisitorNames = new List<string> { "أ. خالد النصار" },
                            VisitorsText = "أ. خالد النصار"
                        },
                        new ActiveDriverInfo
                        {
                            DriverId = 4,
                            DriverCode = "D004",
                            DriverName = "سعد محمد القحطاني",
                            PhoneNumber = "0559876543",
                            DriverPhone = "0559876543",
                            VehicleType = "سيارة",
                            VehicleNumber = "GHI-012",
                            VisitNumber = "V-2025-004",
                            MissionPurpose = "زيارة مشروع التطوير العمراني",
                            DepartureDate = DateTime.Now.AddDays(-3),
                            ReturnDate = DateTime.Now,
                            DaysCount = 3,
                            RemainingDays = 0,
                            Status = "ينتهي اليوم",
                            StatusColor = "#FF9800",
                            DepartureDateText = $"من: {DateTime.Now.AddDays(-3):dd/MM/yyyy}",
                            ReturnDateText = $"إلى: {DateTime.Now:dd/MM/yyyy}",
                            RemainingDaysText = "ينتهي اليوم",
                            ProjectNames = new List<string> { "مشروع التطوير العمراني" },
                            ProjectsText = "مشروع التطوير العمراني",
                            VisitorNames = new List<string> { "م. عبدالله الشمري" },
                            VisitorsText = "م. عبدالله الشمري"
                        }
                    };

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء {sampleData.Count} عنصر تجريبي للعرض");

                    // طباعة تفاصيل أول عنصر للتشخيص
                    if (sampleData.Count > 0)
                    {
                        var first = sampleData[0];
                        System.Diagnostics.Debug.WriteLine($"🔍 أول عنصر تجريبي: {first.DriverName} - الحالة: {first.Status}");
                        System.Diagnostics.Debug.WriteLine($"🔍 المشاريع: {first.ProjectsText}");
                        System.Diagnostics.Debug.WriteLine($"🔍 القائمين: {first.VisitorsText}");
                    }

                    return sampleData;
                }

                foreach (var visit in allVisits)
                {
                    // استخدام أول سائق متاح أو إنشاء سائق افتراضي
                    var driver = allDrivers.FirstOrDefault() ?? new Driver
                    {
                        Id = 1,
                        Name = "سائق افتراضي",
                        DriverCode = "D001",
                        PhoneNumber = "0500000000",
                        VehicleType = "باص",
                        VehicleNumber = "ABC-123"
                    };

                    var remainingDays = (int)(visit.ReturnDate.Date - today).TotalDays;
                    var status = remainingDays <= 0 ? "منتهي" :
                               remainingDays <= 1 ? "ينتهي اليوم" :
                               remainingDays <= 2 ? "ينتهي غداً" : "نشط";

                    var activeDriverInfo = new ActiveDriverInfo
                    {
                        DriverId = driver.Id,
                        DriverCode = driver.DriverCode,
                        DriverName = driver.Name,
                        PhoneNumber = driver.PhoneNumber,
                        DriverPhone = driver.PhoneNumber,
                        VehicleType = driver.VehicleType,
                        VehicleNumber = driver.VehicleNumber,

                        // معلومات التكليف من الزيارة الحقيقية
                        VisitNumber = visit.VisitNumber,
                        MissionPurpose = visit.MissionPurpose,
                        DepartureDate = visit.DepartureDate,
                        ReturnDate = visit.ReturnDate,
                        DaysCount = visit.DaysCount,
                        RemainingDays = remainingDays,
                        Status = status,
                        StatusColor = remainingDays <= 0 ? "#F44336" :
                                    remainingDays <= 1 ? "#FF9800" :
                                    remainingDays <= 2 ? "#FF5722" : "#4CAF50",
                        DepartureDateText = $"من: {visit.DepartureDate:dd/MM/yyyy}",
                        ReturnDateText = $"إلى: {visit.ReturnDate:dd/MM/yyyy}",
                        RemainingDaysText = remainingDays <= 0 ? "انتهت" : $"{remainingDays} يوم متبقي",

                        // المشاريع من البيانات الحقيقية
                        ProjectNames = visit.Projects?.Any() == true
                            ? visit.Projects.Select(p => p.ProjectName ?? "مشروع غير محدد").ToList()
                            : new List<string> { visit.MissionPurpose ?? "مشروع غير محدد" },
                        ProjectsText = visit.Projects?.Any() == true
                            ? string.Join("، ", visit.Projects.Select(p => p.ProjectName ?? "مشروع غير محدد"))
                            : visit.MissionPurpose ?? "مشروع غير محدد",

                        // القائمين بالزيارة من البيانات الحقيقية
                        VisitorNames = visit.Visitors?.Any() == true
                            ? visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}").ToList()
                            : new List<string> { "قائم بالزيارة" },
                        VisitorsText = visit.Visitors?.Any() == true
                            ? string.Join("، ", visit.Visitors.Select(v => $"{v.OfficerRank} {v.OfficerName}"))
                            : "قائم بالزيارة"
                    };

                    activeDrivers.Add(activeDriverInfo);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة الزيارة: {visit.VisitNumber} مع السائق: {driver.Name}");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحضير {activeDrivers.Count} زيارة نشطة");

                return activeDrivers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب السائقين النشطين: {ex.Message}");
                return new List<ActiveDriverInfo>();
            }
        }

        /// <summary>
        /// البحث عن السائق الفائز لزيارة معينة
        /// </summary>
        private async Task<Driver> GetWinnerDriverForVisitAsync(string visitNumber)
        {
            try
            {
                // البحث في جدول العروض عن السائق الفائز
                var winnerQuote = await _context.DriverQuotes
                    .Where(dq => dq.VisitNumber == visitNumber && dq.Status == QuoteStatus.Accepted)
                    .FirstOrDefaultAsync();

                if (winnerQuote != null)
                {
                    // البحث عن السائق بالاسم
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.Name == winnerQuote.DriverName);
                    
                    if (driver != null)
                    {
                        return driver;
                    }
                }

                // إذا لم نجد في العروض، نبحث في حقل DriverContract في الزيارة
                var visit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (visit != null && !string.IsNullOrEmpty(visit.DriverContract))
                {
                    var driver = await _context.Drivers
                        .FirstOrDefaultAsync(d => d.DriverCode.Contains(visit.DriverContract) || 
                                                 visit.DriverContract.Contains(d.DriverCode));
                    return driver;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن السائق الفائز للزيارة {visitNumber}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// جلب إحصائيات الوصول السريع
        /// </summary>
        public async Task<QuickAccessStatistics> GetQuickAccessStatisticsAsync()
        {
            try
            {
                var today = DateTime.Now.Date;
                var tomorrow = today.AddDays(1);

                var totalActiveDrivers = await _context.Drivers.CountAsync(d => d.IsActive);
                
                var activeVisits = await _context.FieldVisits
                    .Where(fv => fv.ReturnDate.Date >= today)
                    .ToListAsync();

                var driversInField = 0;
                var endingToday = 0;
                var endingTomorrow = 0;

                foreach (var visit in activeVisits)
                {
                    var hasDriver = await GetWinnerDriverForVisitAsync(visit.VisitNumber) != null;
                    if (hasDriver)
                    {
                        if (visit.DepartureDate.Date <= today && visit.ReturnDate.Date >= today)
                        {
                            driversInField++;
                        }

                        if (visit.ReturnDate.Date == today)
                        {
                            endingToday++;
                        }
                        else if (visit.ReturnDate.Date == tomorrow)
                        {
                            endingTomorrow++;
                        }
                    }
                }

                return new QuickAccessStatistics
                {
                    TotalActiveDrivers = totalActiveDrivers,
                    DriversInField = driversInField,
                    ActiveVisits = activeVisits.Count,
                    EndingToday = endingToday,
                    EndingTomorrow = endingTomorrow
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب إحصائيات الوصول السريع: {ex.Message}");
                return new QuickAccessStatistics();
            }
        }

        /// <summary>
        /// فلترة السائقين النشطين حسب المعايير
        /// </summary>
        public List<ActiveDriverInfo> FilterActiveDrivers(List<ActiveDriverInfo> drivers, QuickAccessFilter filter)
        {
            System.Diagnostics.Debug.WriteLine($"🔍 FilterActiveDrivers: بدء فلترة {drivers.Count} سائق");

            var filteredDrivers = drivers.AsQueryable();

            // فلترة النص
            if (!string.IsNullOrWhiteSpace(filter.SearchText))
            {
                var searchText = filter.SearchText.ToLower();
                filteredDrivers = filteredDrivers.Where(d => 
                    d.DriverName.ToLower().Contains(searchText) ||
                    d.DriverCode.ToLower().Contains(searchText) ||
                    d.VisitNumber.ToLower().Contains(searchText) ||
                    d.MissionPurpose.ToLower().Contains(searchText));
            }

            // فلترة المتواجدين بالميدان فقط
            if (filter.ShowOnlyInField)
            {
                System.Diagnostics.Debug.WriteLine($"🔍 تطبيق فلتر ShowOnlyInField");
                var beforeCount = filteredDrivers.Count();
                filteredDrivers = filteredDrivers.Where(d =>
                    d.Status == "في الميدان" ||
                    d.Status == "نشط" ||
                    d.Status == "ينتهي اليوم" ||
                    d.Status == "ينتهي غداً");
                var afterCount = filteredDrivers.Count();
                System.Diagnostics.Debug.WriteLine($"🔍 فلتر ShowOnlyInField: {beforeCount} -> {afterCount}");
            }

            // فلترة المنتهية اليوم
            if (filter.ShowEndingToday)
            {
                filteredDrivers = filteredDrivers.Where(d => d.RemainingDays == 0);
            }

            // فلترة المنتهية غداً
            if (filter.ShowEndingTomorrow)
            {
                filteredDrivers = filteredDrivers.Where(d => d.RemainingDays == 1);
            }

            // فلترة التاريخ
            if (filter.FilterDate.HasValue)
            {
                var filterDate = filter.FilterDate.Value.Date;
                filteredDrivers = filteredDrivers.Where(d => 
                    d.DepartureDate.Date <= filterDate && d.ReturnDate.Date >= filterDate);
            }

            return filteredDrivers.ToList();
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للاختبار
        /// </summary>
        private List<ActiveDriverInfo> GetSampleData()
        {
            var sampleData = new List<ActiveDriverInfo>();
            var random = new Random();

            var sampleDrivers = new[]
            {
                new { Name = "أحمد محمد السعيد", Code = "D001", Phone = "0501234567" },
                new { Name = "محمد عبدالله الأحمد", Code = "D002", Phone = "0509876543" },
                new { Name = "عبدالرحمن صالح المطيري", Code = "D003", Phone = "0551234567" },
                new { Name = "خالد عبدالعزيز النصار", Code = "D004", Phone = "0559876543" },
                new { Name = "سعد محمد الدوسري", Code = "D005", Phone = "0561234567" }
            };

            var sampleProjects = new[]
            {
                "مشروع تطوير الرياض الشرقية",
                "مشروع الإسكان الحكومي",
                "مشروع تطوير المنطقة الصناعية",
                "مشروع البنية التحتية",
                "مشروع التطوير العمراني"
            };

            var samplePurposes = new[]
            {
                "زيارة ميدانية للمشروع",
                "متابعة سير العمل",
                "تقييم الجودة والمطابقة",
                "اجتماع مع المقاولين",
                "مراجعة التقدم المحرز"
            };

            for (int i = 0; i < sampleDrivers.Length; i++)
            {
                var driver = sampleDrivers[i];
                var departureDate = DateTime.Now.AddDays(-random.Next(1, 5));
                var returnDate = departureDate.AddDays(random.Next(2, 8));
                var daysCount = (int)(returnDate - departureDate).TotalDays;
                var remainingDays = (int)(returnDate - DateTime.Now).TotalDays;

                sampleData.Add(new ActiveDriverInfo
                {
                    DriverName = driver.Name,
                    DriverCode = driver.Code,
                    DriverPhone = driver.Phone,
                    MissionPurpose = samplePurposes[random.Next(samplePurposes.Length)],
                    DepartureDate = departureDate,
                    ReturnDate = returnDate,
                    DaysCount = daysCount,
                    RemainingDays = remainingDays,
                    ProjectsText = sampleProjects[random.Next(sampleProjects.Length)],
                    VisitorsText = $"المهندس {driver.Name.Split(' ')[0]} والفريق المرافق",
                    Status = remainingDays <= 0 ? "منتهي" : remainingDays <= 1 ? "ينتهي اليوم" : remainingDays <= 2 ? "ينتهي غداً" : "نشط",
                    StatusColor = remainingDays <= 0 ? "#F44336" : remainingDays <= 1 ? "#FF9800" : remainingDays <= 2 ? "#FF5722" : "#4CAF50",
                    DepartureDateText = $"من: {departureDate:dd/MM/yyyy}",
                    ReturnDateText = $"إلى: {returnDate:dd/MM/yyyy}",
                    RemainingDaysText = remainingDays <= 0 ? "انتهت" : $"{remainingDays} يوم متبقي"
                });
            }

            return sampleData;
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
