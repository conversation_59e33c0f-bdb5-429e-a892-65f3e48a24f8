using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using Prism.Commands;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للوصول السريع - عرض السائقين المتواجدين بالميدان
    /// </summary>
    public class QuickAccessViewModel : INotifyPropertyChanged
    {
        private readonly QuickAccessService _quickAccessService;
        private DispatcherTimer _refreshTimer;
        
        private ObservableCollection<ActiveDriverInfo> _activeDrivers;
        private ObservableCollection<ActiveDriverInfo> _filteredDrivers;
        private QuickAccessStatistics _statistics;
        private QuickAccessFilter _filter;
        private bool _isLoading;
        private string _searchText;
        private bool _showOnlyInField = true;
        private bool _showEndingToday;
        private bool _showEndingTomorrow;

        public QuickAccessViewModel()
        {
            _quickAccessService = new QuickAccessService();
            _filter = new QuickAccessFilter();
            
            ActiveDrivers = new ObservableCollection<ActiveDriverInfo>();
            FilteredDrivers = new ObservableCollection<ActiveDriverInfo>();
            Statistics = new QuickAccessStatistics();

            InitializeCommands();
            InitializeAutoRefresh();
            
            // تحميل البيانات عند بدء التشغيل
            _ = LoadDataAsync();
        }

        #region Properties

        public ObservableCollection<ActiveDriverInfo> ActiveDrivers
        {
            get => _activeDrivers;
            set
            {
                _activeDrivers = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<ActiveDriverInfo> FilteredDrivers
        {
            get => _filteredDrivers;
            set
            {
                _filteredDrivers = value;
                OnPropertyChanged();
            }
        }

        public QuickAccessStatistics Statistics
        {
            get => _statistics;
            set
            {
                _statistics = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                _searchText = value;
                _filter.SearchText = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool ShowOnlyInField
        {
            get => _showOnlyInField;
            set
            {
                _showOnlyInField = value;
                _filter.ShowOnlyInField = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool ShowEndingToday
        {
            get => _showEndingToday;
            set
            {
                _showEndingToday = value;
                _filter.ShowEndingToday = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public bool ShowEndingTomorrow
        {
            get => _showEndingTomorrow;
            set
            {
                _showEndingTomorrow = value;
                _filter.ShowEndingTomorrow = value;
                OnPropertyChanged();
                ApplyFilters();
            }
        }

        public string LastUpdateTime { get; private set; } = DateTime.Now.ToString("HH:mm:ss");

        #endregion

        #region Commands

        public ICommand RefreshCommand { get; private set; }
        public ICommand ClearFiltersCommand { get; private set; }
        public ICommand SelectAllCommand { get; private set; }
        public ICommand ClearSelectionCommand { get; private set; }

        private void InitializeCommands()
        {
            RefreshCommand = new DelegateCommand(async () => await LoadDataAsync());
            ClearFiltersCommand = new DelegateCommand(ClearFilters);
            SelectAllCommand = new DelegateCommand(SelectAll);
            ClearSelectionCommand = new DelegateCommand(ClearSelection);
        }

        #endregion

        #region Methods

        /// <summary>
        /// تحميل البيانات من قاعدة البيانات
        /// </summary>
        public async Task LoadDataAsync()
        {
            try
            {
                IsLoading = true;
                System.Diagnostics.Debug.WriteLine("🔄 بدء تحميل بيانات الوصول السريع...");

                // تأخير قصير لضمان ظهور مؤشر التحميل
                await Task.Delay(300);

                // تحميل السائقين النشطين
                var activeDrivers = await _quickAccessService.GetActiveDriversInFieldAsync();

                // تحميل الإحصائيات
                var statistics = await _quickAccessService.GetQuickAccessStatisticsAsync();

                // تحديث الواجهة في الخيط الرئيسي
                App.Current.Dispatcher.Invoke(() =>
                {
                    ActiveDrivers.Clear();
                    foreach (var driver in activeDrivers)
                    {
                        ActiveDrivers.Add(driver);
                    }

                    Statistics = statistics;
                    ApplyFilters();

                    LastUpdateTime = DateTime.Now.ToString("HH:mm:ss");
                    OnPropertyChanged(nameof(LastUpdateTime));
                });

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {activeDrivers.Count} سائق نشط - عدد المفلترين: {FilteredDrivers.Count}");

                // طباعة تفاصيل أول سائق للتشخيص
                if (FilteredDrivers.Count > 0)
                {
                    var firstDriver = FilteredDrivers[0];
                    System.Diagnostics.Debug.WriteLine($"🔍 أول سائق: {firstDriver.DriverName}");
                    System.Diagnostics.Debug.WriteLine($"   📋 المهمة: {firstDriver.MissionPurpose}");
                    System.Diagnostics.Debug.WriteLine($"   📅 التواريخ: {firstDriver.DepartureDateText} - {firstDriver.ReturnDateText}");
                    System.Diagnostics.Debug.WriteLine($"   🎯 المشاريع: {firstDriver.ProjectsText}");
                    System.Diagnostics.Debug.WriteLine($"   👥 القائمين: {firstDriver.VisitorsText}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل بيانات الوصول السريع: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");
            }
            finally
            {
                IsLoading = false;
                System.Diagnostics.Debug.WriteLine($"🏁 انتهاء التحميل - IsLoading = {IsLoading}");
            }
        }

        /// <summary>
        /// تطبيق الفلاتر على البيانات
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                var filtered = _quickAccessService.FilterActiveDrivers(ActiveDrivers.ToList(), _filter);
                
                FilteredDrivers.Clear();
                foreach (var driver in filtered)
                {
                    FilteredDrivers.Add(driver);
                }

                System.Diagnostics.Debug.WriteLine($"🔍 تم فلترة {filtered.Count} من أصل {ActiveDrivers.Count} سائق");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تطبيق الفلاتر: {ex.Message}");
            }
        }

        /// <summary>
        /// مسح جميع الفلاتر
        /// </summary>
        private void ClearFilters()
        {
            SearchText = string.Empty;
            ShowOnlyInField = true;
            ShowEndingToday = false;
            ShowEndingTomorrow = false;
            _filter.FilterDate = null;
        }

        /// <summary>
        /// تحديد جميع السائقين
        /// </summary>
        private void SelectAll()
        {
            foreach (var driver in FilteredDrivers)
            {
                driver.IsSelected = true;
            }
        }

        /// <summary>
        /// إلغاء تحديد جميع السائقين
        /// </summary>
        private void ClearSelection()
        {
            foreach (var driver in FilteredDrivers)
            {
                driver.IsSelected = false;
            }
        }

        /// <summary>
        /// إعداد التحديث التلقائي كل 5 دقائق
        /// </summary>
        private void InitializeAutoRefresh()
        {
            _refreshTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(5) // تحديث كل 5 دقائق
            };
            
            _refreshTimer.Tick += async (sender, e) => await LoadDataAsync();
            _refreshTimer.Start();
            
            System.Diagnostics.Debug.WriteLine("⏰ تم تفعيل التحديث التلقائي كل 5 دقائق");
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _quickAccessService?.Dispose();
        }

        #endregion
    }
}
